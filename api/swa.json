{"openapi": "3.1.0", "info": {"title": "Mbaza API Documentation", "version": "V1.0"}, "servers": [{"url": "https://api.dev.mbaza.awesomity.rw", "description": "Generated server url"}], "tags": [{"name": "Upload Files", "description": "Endpoints for submitting files"}, {"name": "Location", "description": "Endpoints for managing locations in Rwanda"}, {"name": "Permissions", "description": "Permissions management endpoints"}, {"name": "Organizations management", "description": "Organizations management endpoints"}, {"name": "Profiles", "description": "Profile endpoints"}, {"name": "Staff management", "description": "Staff management endpoints"}, {"name": "Citizens", "description": "Citizens endpoints Management"}, {"name": "Notifications", "description": "Endpoints for Managing notifications"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Endpoints for submitting and managing complaints"}, {"name": "Users management", "description": "Users management endpoints"}, {"name": "Authentication", "description": "Authentication endpoints"}, {"name": "Roles management", "description": "Roles management endpoints"}, {"name": "Cases", "description": "Endpoints for submitting and Managing cases"}, {"name": "Categories", "description": "End point to manage categories"}], "paths": {"/api/v1/users/nid-verification": {"post": {"tags": ["Users management"], "summary": "Validate national ID", "operationId": "checkNationalId", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Output"}}}}}}}, "/api/v1/staff": {"get": {"tags": ["Staff management"], "summary": "Get all staff members with pagination", "operationId": "getAllStaff", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "q", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "organizationId", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Staff management"], "summary": "Create a new staff member with profile picture", "operationId": "createStaff", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.CreateStaffDTO.Input"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/roles": {"get": {"tags": ["Roles management"], "summary": "<PERSON><PERSON> all roles", "operationId": "getAllRoles", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "q", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "isExpired", "in": "query", "required": false, "schema": {"type": "string", "enum": ["EXPIRED", "NOT_EXPIRED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Roles management"], "summary": "Create role", "operationId": "createRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.CreateRoleDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/roles/{roleId}/users/{userId}": {"post": {"tags": ["Roles management"], "summary": "Assign role to user", "operationId": "assignRoleToUser", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Roles management"], "summary": "Remove role from user", "operationId": "removeRoleFromUser", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/roles/{roleId}/permissions": {"post": {"tags": ["Roles management"], "summary": "Add permissions to a role", "operationId": "addPermissionsToRole", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.AddPermissionsToRoleDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Roles management"], "summary": "Remove permissions from a role", "operationId": "removePermissionsFromRole", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.RemovePermissionsFromRoleDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/organizations": {"get": {"tags": ["Organizations management"], "summary": "Get all organizations with pagination", "operationId": "getAllOrganizations", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Organizations management"], "summary": "Create a new organization with admin user", "operationId": "createOrganization", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.CreateOrganizationDTO.Input"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/files": {"post": {"tags": ["Upload Files"], "summary": "Upload multiple files for complaints", "operationId": "uploadFiles", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}, "required": ["files"]}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO.Output"}}}}}}}, "/api/v1/complaints": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Get all complaints with filtering and search capabilities", "description": "Retrieve complaints with advanced filtering options:\n- **scope**: Filter by time period (TODAY, THIS_WEEK, THIS_MONTH)\n- **startDate/endDate**: Filter by custom date range (format: YYYY-MM-DD)\n- **searchTerm**: Search across title, description, identification number, and contact details\n- **title**: Filter by complaint title\n- **location**: Filter by geographic location (district, sector, cell, village)\n- **isClosed**: Filter by open/closed status\n\nNote: Date scope and date range filters can be used independently or together.\nWhen both are provided, both conditions must be satisfied.\n", "operationId": "getAllComplaints", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "title", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.districtId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.sectorId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.cellId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.villageId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "isClosed", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "scope", "in": "query", "required": false, "schema": {"type": "string", "enum": ["TODAY", "THIS_WEEK", "THIS_MONTH"]}}, {"name": "startDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Create a new complaint", "operationId": "createComplaint", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/complaints/get-complaint-tracking-otp": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Get OTP to track the complaint.", "operationId": "getComplaintTrackingOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetCompliantTrackingOtpDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}}}, "/api/v1/complaints/anonymous": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Create a new complaint anonymously", "operationId": "createAnonymousComplaints", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Output"}}}}}}}, "/api/v1/citizens": {"get": {"tags": ["Citizens"], "summary": "View all citizens", "operationId": "getAllCitizens", "parameters": [{"name": "q", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Citizens"], "summary": "Add citizens", "operationId": "addCitizen", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.AddCitizenDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases": {"get": {"tags": ["Cases"], "summary": "Get all cases", "operationId": "getAllCases", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "title", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CLOSED", "RE_OPENED", "ONGOING", "DONE", "REJECTED"]}}, {"name": "categoryId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.districtId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.sectorId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.cellId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.villageId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "is<PERSON><PERSON>", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "institutionId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "severity", "in": "query", "required": false, "schema": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Cases"], "summary": "Create a case ", "operationId": "createCase", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{id}/invitation-response": {"post": {"tags": ["Cases"], "summary": "Accept or reject case invitation", "operationId": "respondToInvitation", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{id}/comments": {"post": {"tags": ["Cases"], "summary": "Add comment to a case", "operationId": "addCommentToCase", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{complaintId}/start-case": {"post": {"tags": ["Cases"], "summary": "Start a case from a complaint", "operationId": "startCase", "parameters": [{"name": "complaintId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.StartCaseDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.StartCaseDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/auth/reset-password": {"post": {"tags": ["Authentication"], "summary": "Reset password", "operationId": "resetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ResetPasswordDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}}}, "/api/v1/auth/request-reset-password": {"post": {"tags": ["Authentication"], "summary": "Request reset password link", "operationId": "requestResetPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.RequestPasswordResetCodeDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["Authentication"], "summary": "Citizen registration", "operationId": "registerUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh access token", "operationId": "refreshToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Output"}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login a user", "operationId": "authenticateUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.LoginDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.LoginDTO.Output"}}}}}}}, "/api/v1/auth/admin/login": {"post": {"tags": ["Authentication"], "summary": "Admin login", "operationId": "authenticateAdmin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.AdminLoginDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.AdminLoginDTO.Output"}}}}}}}, "/api/v1/users/me": {"get": {"tags": ["Users management"], "summary": "Get current user details", "operationId": "getCurrentUserDetails", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewUserProfileDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Users management"], "summary": "Update current user details", "operationId": "updateCurrentUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateDetailsDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/staff/{id}": {"get": {"tags": ["Staff management"], "summary": "Get staff member by ID", "operationId": "getStaffById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewStaffDetailsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Staff management"], "summary": "Delete a staff member", "operationId": "deleteStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Staff management"], "summary": "Update an existing staff member with profile picture", "operationId": "updateStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateStaffDTO.Input"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/staff/{id}/toggle-active": {"patch": {"tags": ["Staff management"], "summary": "Toggle staff member active status", "operationId": "toggleStaffActiveStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/roles/{id}": {"get": {"tags": ["Roles management"], "summary": "Get role by ID", "operationId": "getRoleById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Roles management"], "summary": "Delete role", "operationId": "deleteRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Roles management"], "summary": "Update role", "operationId": "updateRole", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateRoleDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/profile/notification-channels": {"patch": {"tags": ["Profiles"], "operationId": "updateNotificationChannels", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateNotificationChannelsDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/organizations/{id}": {"get": {"tags": ["Organizations management"], "summary": "Get organization by ID", "operationId": "getOrganizationById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.contracts.iam.dtos.ViewOrganizationDetails.Output"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Organizations management"], "summary": "Update an existing organization", "operationId": "updateOrganization", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateOrganizationDTO.Input"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/notifications/{id}/read": {"patch": {"tags": ["Notifications"], "summary": "Mark a notification as read", "operationId": "markNotificationAsRead", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/complaints/{id}/close": {"patch": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Close a complaint", "operationId": "closeCase", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CloseComplaintDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/citizens/{id}": {"get": {"tags": ["Citizens"], "summary": "View citizen details", "operationId": "getCitizenDetails", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewCitizenDetailsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Citizens"], "summary": "Update citizen details", "operationId": "updateCitizenUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.UpdateDetailsDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{id}": {"get": {"tags": ["Cases"], "summary": "Get a case by its ID", "operationId": "getCaseById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Cases"], "summary": "Update case details", "operationId": "updateCaseDetails", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.UpdateCaseDetailsDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{id}/staff/case-status": {"patch": {"tags": ["Cases"], "summary": "Update case Status, by staff", "operationId": "updateCaseStatusByStaff", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForStaffDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/{id}/citizen/case-status": {"patch": {"tags": ["Cases"], "summary": "Update case Status, by citizen", "operationId": "updateCaseStatusByCitizen", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForCitizenDTO.Input"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/roles/organization/{organizationId}": {"get": {"tags": ["Roles management"], "summary": "Fetch  roles by organization ID", "operationId": "getByOrganizationId", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/profile": {"get": {"tags": ["Profiles"], "operationId": "getProfile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewProfileInfoDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/permissions": {"get": {"tags": ["Permissions"], "summary": "Get all permissions", "operationId": "getAllPermissions", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/organizations/download": {"get": {"tags": ["Organizations management"], "summary": "Download Organizations in specified format", "operationId": "downloadOrganizations", "parameters": [{"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "fileFormat", "in": "query", "required": true, "schema": {"type": "string", "enum": ["CSV", "EXCEL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/organizations/analytics": {"get": {"tags": ["Organizations management"], "summary": "Get organization analytics", "operationId": "getOrganizationAnalytics", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.GetOrganizationAnalytics.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/notifications": {"get": {"tags": ["Notifications"], "summary": "Retrieve all notifications for the current user", "operationId": "getAllNotifications", "parameters": [{"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/locations/villages/{cellId}": {"get": {"tags": ["Location"], "summary": "Get villages by cell ID", "operationId": "getVillagesByCellId", "parameters": [{"name": "cellId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewVillageDTO.Output"}}}}}}}, "/api/v1/locations/sectors/{districtId}": {"get": {"tags": ["Location"], "summary": "Get sectors by district ID", "operationId": "getSectorsByDistrictId", "parameters": [{"name": "districtId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewSectorDTO.Output"}}}}}}}, "/api/v1/locations/provinces": {"get": {"tags": ["Location"], "summary": "Get all provinces", "operationId": "getAllProvinces", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.location.dtos.ViewProvinceDTO.Output"}}}}}}}, "/api/v1/locations/districts": {"get": {"tags": ["Location"], "summary": "Get all districts", "operationId": "getAllDistricts", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output"}}}}}}}, "/api/v1/locations/districts/{provinceId}": {"get": {"tags": ["Location"], "summary": "Get districts by province ID", "operationId": "getDistrictsByProvinceId", "parameters": [{"name": "provinceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output"}}}}}}}, "/api/v1/locations/cells/{sectorId}": {"get": {"tags": ["Location"], "summary": "Get cells by sector ID", "operationId": "getCellsBySectorId", "parameters": [{"name": "sectorId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewCellDTO.Output"}}}}}}}, "/api/v1/complaints/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Get a complaint by its ID", "operationId": "getComplaintById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/complaints/tracking": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Track the complaint using otp code.", "operationId": "getComplaintDetails", "parameters": [{"name": "otpCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "identificationNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.Output"}}}}}}}, "/api/v1/complaints/search/{identificationNumber}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Get a complaint by identification number", "operationId": "getComplaintByIndetificationNumber", "parameters": [{"name": "identificationNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.Output"}}}}}}}, "/api/v1/citizens/stats": {"get": {"tags": ["Citizens"], "summary": "Get statics for all Citizens", "operationId": "getCitizenGeneralStats", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewCitizenStatsDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/citizens/download": {"get": {"tags": ["Citizens"], "summary": "Download citizens in specified format", "operationId": "downloadCitizens", "parameters": [{"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "fileFormat", "in": "query", "required": true, "schema": {"type": "string", "enum": ["CSV", "EXCEL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/categories": {"get": {"tags": ["Categories"], "summary": "Get all categories", "operationId": "getAllComplaints_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.complaint.dtos.ViewCategoryDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/download": {"get": {"tags": ["Cases"], "summary": "Download cases in specified format", "operationId": "downloadCases", "parameters": [{"name": "title", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CLOSED", "RE_OPENED", "ONGOING", "DONE", "REJECTED"]}}, {"name": "categoryId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "location.districtId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.sectorId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.cellId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "location.villageId", "in": "query", "required": false, "schema": {"type": "string", "minLength": 1}}, {"name": "is<PERSON><PERSON>", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "institutionId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "severity", "in": "query", "required": false, "schema": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}}, {"name": "fileFormat", "in": "query", "required": true, "schema": {"type": "string", "enum": ["CSV", "EXCEL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/citizen/{citizenId}": {"get": {"tags": ["Cases"], "summary": "Get cases for a citizen by citizenId with optional search", "operationId": "getCasesByCitizenId", "parameters": [{"name": "citizenId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "required": false, "schema": {"type": "integer", "default": 0, "minimum": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "required": false, "schema": {"type": "integer", "default": 20, "minimum": 1}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/cases/citizen/{citizenId}/download": {"get": {"tags": ["Cases"], "summary": "Download cases for a citizen by citizenId with optional search", "operationId": "downloadCasesByCitizenId", "parameters": [{"name": "citizenId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "searchTerm", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "fileFormat", "in": "query", "required": true, "schema": {"type": "string", "enum": ["CSV", "EXCEL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"minaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Input": {"type": "object", "properties": {"nid": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.CheckNationalIdDTO.Output": {"type": "object", "properties": {"foreName": {"type": "string"}, "surnames": {"type": "string"}, "sex": {"type": "string"}, "dateOfBirth": {"type": "string"}, "documentNumber": {"type": "string"}, "nationality": {"type": "string"}, "cell": {"type": "string"}, "sector": {"type": "string"}, "district": {"type": "string"}, "province": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.CreateStaffDTO.Input": {"type": "object", "properties": {"organizationId": {"type": "string"}, "fullName": {"type": "string", "minLength": 1}, "email": {"type": "string", "minLength": 1}, "phoneNumber": {"type": "string", "minLength": 1, "pattern": "^\\+250\\d{9}$"}, "roleIds": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "trackingLevel": {"type": "string", "enum": ["PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"]}, "assignedLocations": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "profilePicture": {"type": "string", "format": "binary"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}}, "required": ["email", "fullName", "phoneNumber", "roleIds"]}, "minaloc.mbaza.api.common.responses.GenericResponseJava.lang.Void": {"type": "object", "properties": {"payload": {}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.CreateRoleDTO.Input": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500, "minLength": 10}, "validFrom": {"type": "string", "format": "date-time"}, "validTo": {"type": "string", "format": "date-time"}}, "required": ["description", "name"]}, "minaloc.mbaza.api.iam.dtos.AddPermissionsToRoleDTO.Input": {"type": "object", "properties": {"permissionIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["permissionIds"]}, "minaloc.mbaza.api.iam.dtos.CreateOrganizationDTO.Input": {"type": "object", "properties": {"organizationName": {"type": "string", "maxLength": 100, "minLength": 2}, "type": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "adminFullName": {"type": "string", "maxLength": 100, "minLength": 2}, "adminEmail": {"type": "string", "minLength": 1}, "adminPhoneNumber": {"type": "string", "minLength": 1, "pattern": "^\\+250\\d{9}$"}, "categoryIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "logoFile": {"type": "string", "format": "binary"}}, "required": ["adminEmail", "admin<PERSON><PERSON><PERSON>ame", "adminPhoneNumber", "categoryIds", "organizationName"]}, "minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO.Output": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO.Output"}}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewFileUploadedDTO.Output": {"type": "object", "properties": {"type": {"type": "string"}, "fileName": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ContactDTO.Input": {"type": "object", "properties": {"email": {"type": "string", "minLength": 1}, "phone": {"type": "string", "minLength": 1, "pattern": "^\\+250\\d{9}$"}, "firstName": {"type": "string", "minLength": 1}, "lastName": {"type": "string", "minLength": 1}, "nid": {"type": "string", "minLength": 1, "pattern": "^[0-9]{16}$"}}, "required": ["email", "firstName", "lastName", "nid", "phone"]}, "minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.DocumentDto": {"type": "object", "properties": {"type": {"type": "string", "minLength": 1}, "fileName": {"type": "string", "minLength": 1}}, "required": ["fileName", "type"]}, "minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Input": {"type": "object", "properties": {"title": {"type": "string", "minLength": 1}, "description": {"type": "string", "minLength": 1}, "location": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.LocationDTO.Input"}, "contact": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ContactDTO.Input"}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.DocumentDto"}, "uniqueItems": true}}, "required": ["description", "title"]}, "minaloc.mbaza.api.complaint.dtos.LocationDTO.Input": {"type": "object", "properties": {"districtId": {"type": "string", "minLength": 1}, "sectorId": {"type": "string", "minLength": 1}, "cellId": {"type": "string", "minLength": 1}, "villageId": {"type": "string", "minLength": 1}}, "required": ["cellId", "districtId", "sectorId", "villageId"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.CreateComplaintDTO.Output": {"type": "object", "properties": {"complaintId": {"type": "string"}, "identificationNumber": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetCompliantTrackingOtpDTO.Input": {"type": "object", "properties": {"identificationNumber": {"type": "string", "minLength": 1}}, "required": ["identificationNumber"]}, "minaloc.mbaza.api.iam.dtos.AddCitizenDTO.Input": {"type": "object", "properties": {"fullName": {"type": "string", "maxLength": 100, "minLength": 2}, "nationalIdNumber": {"type": "string", "minLength": 1, "pattern": "^[0-9]{16}$"}, "phoneNumber": {"type": "string", "minLength": 1, "pattern": "^\\+250\\d{9}$"}, "email": {"type": "string", "minLength": 1}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}}, "required": ["email", "fullName", "nationalIdNumber", "phoneNumber"]}, "minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.DocumentDto": {"type": "object", "properties": {"type": {"type": "string", "minLength": 1}, "fileName": {"type": "string", "minLength": 1}}, "required": ["fileName", "type"]}, "minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Input": {"type": "object", "properties": {"title": {"type": "string", "minLength": 1}, "description": {"type": "string", "minLength": 1}, "location": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.LocationDTO.Input"}, "contact": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ContactDTO.Input"}, "categoryIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "primaryOrganizationId": {"type": "string", "minLength": 1}, "organizationIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "severity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "trackingLevel": {"type": "string", "enum": ["DISTRICT", "SECTOR", "CELL", "VILLAGE"]}, "estimatedResolutionDays": {"type": "integer", "format": "int32"}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.DocumentDto"}, "uniqueItems": true}}, "required": ["description", "primaryOrganizationId", "title", "trackingLevel"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.CreateCaseDTO.Output": {"type": "object", "properties": {"caseId": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Input": {"type": "object", "properties": {"action": {"type": "string", "enum": ["ACCEPTED", "REJECTED"]}, "reason": {"type": "string"}}, "required": ["action"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.InvitationResponseDTO.Output": {"type": "object", "properties": {"message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.DocumentDto": {"type": "object", "properties": {"type": {"type": "string", "minLength": 1}, "fileName": {"type": "string", "minLength": 1}}, "required": ["fileName", "type"]}, "minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Input": {"type": "object", "properties": {"comment": {"type": "string", "minLength": 1}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.DocumentDto"}, "uniqueItems": true}, "visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}}, "required": ["comment"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.CaseCommentDTO.Output": {"type": "object", "properties": {"message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.StartCaseDTO.Input": {"type": "object", "properties": {"categoryIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "primaryOrganizationId": {"type": "string", "minLength": 1}, "organizationIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "severity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "trackingLevel": {"type": "string", "enum": ["DISTRICT", "SECTOR", "CELL", "VILLAGE"]}, "estimatedResolutionDays": {"type": "integer", "format": "int32"}}, "required": ["primaryOrganizationId", "trackingLevel"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.StartCaseDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.StartCaseDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.StartCaseDTO.Output": {"type": "object", "properties": {"caseId": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ResetPasswordDTO.Input": {"type": "object", "properties": {"code": {"type": "string", "minLength": 1}, "newPassword": {"type": "string", "maxLength": 2147483647, "minLength": 4}}, "required": ["code", "newPassword"]}, "minaloc.mbaza.api.iam.dtos.RequestPasswordResetCodeDTO.Input": {"type": "object", "properties": {"identifier": {"type": "string", "minLength": 1}, "notificationMethod": {"type": "string", "enum": ["EMAIL", "SMS"]}}, "required": ["identifier"]}, "minaloc.mbaza.api.iam.dtos.RegisterCitizenDTO.Input": {"type": "object", "properties": {"fullName": {"type": "string", "minLength": 1}, "nationalIdNumber": {"type": "string", "minLength": 1, "pattern": "^[0-9]{16}$"}, "phoneNumber": {"type": "string", "pattern": "^\\+250\\d{9}$"}, "email": {"type": "string"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "password": {"type": "string", "maxLength": 2147483647, "minLength": 4}}, "required": ["fullName", "nationalIdNumber", "password"]}, "minaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Input": {"type": "object", "properties": {"refreshToken": {"type": "string", "minLength": 1}}, "required": ["refreshToken"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.RefreshTokenDTO.Output": {"type": "object", "properties": {"accessToken": {"type": "string"}, "refreshToken": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.LoginDTO.Input": {"type": "object", "properties": {"identifier": {"type": "string", "minLength": 1}, "password": {"type": "string", "minLength": 1}}, "required": ["identifier", "password"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.LoginDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.LoginDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.LoginDTO.Output": {"type": "object", "properties": {"accessToken": {"type": "string"}, "refreshToken": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.AdminLoginDTO.Input": {"type": "object", "properties": {"email": {"type": "string", "minLength": 1}, "password": {"type": "string", "minLength": 1}}, "required": ["email", "password"]}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.AdminLoginDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.AdminLoginDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.AdminLoginDTO.Output": {"type": "object", "properties": {"accessToken": {"type": "string"}, "refreshToken": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.UpdateDetailsDTO.Input": {"type": "object", "properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3}, "fullName": {"type": "string", "maxLength": 100, "minLength": 2}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}}}, "minaloc.mbaza.api.iam.dtos.UpdateStaffDTO.Input": {"type": "object", "properties": {"fullName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string", "pattern": "^\\+250\\d{9}$"}, "roleIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "trackingLevel": {"type": "string", "enum": ["PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"]}, "assignedLocations": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "profilePicture": {"type": "string", "format": "binary"}}}, "minaloc.mbaza.api.iam.dtos.RemovePermissionsFromRoleDTO.Input": {"type": "object", "properties": {"permissionIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["permissionIds"]}, "minaloc.mbaza.api.iam.dtos.UpdateRoleDTO.Input": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 50, "minLength": 2}, "description": {"type": "string", "maxLength": 500, "minLength": 10}, "validFrom": {"type": "string", "format": "date-time"}, "validTo": {"type": "string", "format": "date-time"}}}, "minaloc.mbaza.api.iam.dtos.UpdateNotificationChannelsDTO.Input": {"type": "object", "properties": {"EMAIL": {"type": "boolean"}, "SMS": {"type": "boolean"}, "WHATSAPP": {"type": "boolean"}}}, "minaloc.mbaza.api.iam.dtos.UpdateOrganizationDTO.Input": {"type": "object", "properties": {"organizationName": {"type": "string", "maxLength": 100, "minLength": 2}, "type": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "logoFile": {"type": "string", "format": "binary"}}, "required": ["organizationName", "type"]}, "minaloc.mbaza.api.complaint.dtos.CloseComplaintDTO.Input": {"type": "object", "properties": {"comment": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.UpdateCaseDetailsDTO.Input": {"type": "object", "properties": {"categoryIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "organizationIds": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "primaryOrganizationId": {"type": "string"}, "severity": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "trackingLevel": {"type": "string"}, "estimatedResolutionDays": {"type": "integer", "format": "int32"}}}, "minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForStaffDTO.CreateDocumentDTO": {"type": "object", "properties": {"type": {"type": "string", "minLength": 1}, "fileName": {"type": "string", "minLength": 1}}, "required": ["fileName", "type"]}, "minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForStaffDTO.Input": {"type": "object", "properties": {"status": {"type": "string", "enum": ["CLOSED", "REJECTED", "ONGOING", "RE_OPENED", "DONE"]}, "comment": {"type": "string"}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForStaffDTO.CreateDocumentDTO"}, "uniqueItems": true}}}, "minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForCitizenDTO.CreateDocumentDTO": {"type": "object", "properties": {"type": {"type": "string", "minLength": 1}, "fileName": {"type": "string", "minLength": 1}}, "required": ["fileName", "type"]}, "minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForCitizenDTO.Input": {"type": "object", "properties": {"status": {"type": "string", "enum": ["DONE", "RE_OPENED"]}, "comment": {"type": "string"}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.UpdateCaseStatusForCitizenDTO.CreateDocumentDTO"}, "uniqueItems": true}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewUserProfileDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewUserProfileDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "username": {"type": "string"}, "fullName": {"type": "string"}, "gender": {"type": "string"}, "phoneNumber": {"type": "string"}, "nationalIdNumber": {"type": "string"}, "organizationId": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string"}, "phoneNumber": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "lastLoginAt": {"type": "string", "format": "date-time"}, "activatedAt": {"type": "string", "format": "date-time"}, "profilePicture": {"type": "string"}}}, "org.springframework.data.web.PagedModel.PageMetadata": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "number": {"type": "integer", "format": "int64"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int64"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewStaffDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewStaffDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewStaffDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.contracts.location.dtos.GetAssignedLocations.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "code": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewStaffDetailsDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "trackingLevel": {"type": "string", "enum": ["PROVINCE", "DISTRICT", "SECTOR", "CELL", "VILLAGE"]}, "assignedLocations": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.contracts.location.dtos.GetAssignedLocations.Output"}}, "occupation": {"type": "string"}, "profilePicture": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "assignedUsersCount": {"type": "integer", "format": "int64"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewAllRolesDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "validFrom": {"type": "string", "format": "date-time"}, "validTo": {"type": "string", "format": "date-time"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.ViewPermissionDTO"}}}}, "minaloc.mbaza.api.iam.dtos.ViewRoleDetailsDTO.ViewPermissionDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewProfileInfoDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewProfileInfoDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewProfileInfoDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "channelsMap": {"type": "object", "additionalProperties": {"type": "boolean"}}, "notificationChannelsFromSet": {"type": "array", "items": {"type": "string", "enum": ["SMS", "EMAIL", "WHATSAPP"]}, "uniqueItems": true, "writeOnly": true}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewPermissionDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "createdAt": {"type": "string", "format": "date-time"}, "staffCount": {"type": "integer", "format": "int64"}, "complaintCount": {"type": "integer", "format": "int64"}, "logoUrl": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewOrganizationDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.contracts.iam.dtos.ViewOrganizationDetails.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.contracts.iam.dtos.ViewOrganizationDetails.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.contracts.iam.dtos.ViewOrganizationDetails.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["PUBLIC", "PRIVATE"]}, "createdAt": {"type": "string", "format": "date-time"}, "adminId": {"type": "string"}, "adminEmail": {"type": "string"}, "adminFullName": {"type": "string"}, "logoUrl": {"type": "string"}, "staffCount": {"type": "integer", "format": "int64"}, "complaintCount": {"type": "integer", "format": "int64"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.GetOrganizationAnalytics.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.GetOrganizationAnalytics.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.GetOrganizationAnalytics.Output": {"type": "object", "properties": {"totalOrganizations": {"type": "integer", "format": "int64"}, "totalOrganizationWithCases": {"type": "integer", "format": "int64"}, "averageCasesPerOrganization": {"type": "number", "format": "double"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "content": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "senderName": {"type": "string"}, "senderId": {"type": "string"}, "readAt": {"type": "string", "format": "date-time"}, "caseId": {"type": "string"}, "read": {"type": "boolean"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.notifications.dtos.ViewInternalNotificationDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewVillageDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewVillageDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.location.dtos.ViewVillageDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewVillageDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewVillageDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewSectorDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewSectorDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.location.dtos.ViewSectorDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewSectorDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewSectorDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.location.dtos.ViewProvinceDTO.Output": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewProvinceDTO.Output"}}, "message": {"type": "string"}}}, "minaloc.mbaza.api.location.dtos.ViewProvinceDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output"}}, "message": {"type": "string"}}}, "minaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output"}, "message": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewDistrictDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewCellDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewCellDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.location.dtos.ViewCellDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.location.dtos.ViewCellDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.location.dtos.ViewCellDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "identificationNumber": {"type": "string"}, "title": {"type": "string"}, "createdFor": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ContactDTO": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "nid": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.LocationDTO": {"type": "object", "properties": {"districtName": {"type": "string"}, "sectorName": {"type": "string"}, "cellName": {"type": "string"}, "villageName": {"type": "string"}, "districtId": {"type": "string"}, "sectorId": {"type": "string"}, "cellId": {"type": "string"}, "villageId": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.Output": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "location": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.LocationDTO"}, "contact": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ContactDTO"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ViewDocumentDTO"}, "uniqueItems": true}, "followUps": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ViewFollowUpDTO"}, "uniqueItems": true}, "createdAt": {"type": "string", "format": "date-time"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ViewDocumentDTO": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewComplaintDetailsDTO.ViewFollowUpDTO": {"type": "object", "properties": {"comment": {"type": "string"}, "createdByUserName": {"type": "string"}, "position": {"type": "string"}, "summary": {"type": "string"}, "documentUrls": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "createdAt": {"type": "string", "format": "date-time"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.CaseDTO": {"type": "object", "properties": {"status": {"type": "string"}, "severity": {"type": "string"}, "trackingLevel": {"type": "string"}, "estimatedResolutionDate": {"type": "string", "format": "date-time"}, "estimatedResolutionDays": {"type": "integer", "format": "int32"}, "isUrgent": {"type": "boolean"}, "owner": {"type": "string"}, "timeline": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.TimelineDTO"}}, "informedInstitutions": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.OrganizationDTO"}}, "supportingDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.SupportingDocumentDTO"}}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.ContactDTO": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "nid": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.LocationDTO": {"type": "object", "properties": {"districtName": {"type": "string"}, "sectorName": {"type": "string"}, "cellName": {"type": "string"}, "villageName": {"type": "string"}, "districtId": {"type": "string"}, "sectorId": {"type": "string"}, "cellId": {"type": "string"}, "villageId": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.OrganizationDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.Output": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "location": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.LocationDTO"}, "contact": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.ContactDTO"}, "createdAt": {"type": "string", "format": "date-time"}, "isClosed": {"type": "boolean"}, "complaintCase": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.CaseDTO"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.SupportingDocumentDTO": {"type": "object", "properties": {"type": {"type": "string"}, "fileName": {"type": "string"}, "url": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.GetComplaintDetailsDTO.TimelineDTO": {"type": "object", "properties": {"action": {"type": "string"}, "actionMetadata": {"type": "object", "additionalProperties": {}}, "performedByUserId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string"}, "phoneNumber": {"type": "string"}, "complaintCount": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.GetAllCitizenDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewCitizenDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewCitizenDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewCitizenDetailsDTO.Output": {"type": "object", "properties": {"fullName": {"type": "string"}, "email": {"type": "string"}, "phoneNumber": {"type": "string"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "nationalIdNumber": {"type": "string"}, "casesCount": {"type": "integer", "format": "int64"}, "casesOngoingCount": {"type": "integer", "format": "int64"}, "casesClosedCount": {"type": "integer", "format": "int64"}, "id": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.iam.dtos.ViewCitizenStatsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.iam.dtos.ViewCitizenStatsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.iam.dtos.ViewCitizenStatsDTO.Output": {"type": "object", "properties": {"totalCitizens": {"type": "integer", "format": "int64"}, "citizensWithOngoingCases": {"type": "integer", "format": "int64"}, "averageCasesPerCitizen": {"type": "integer", "format": "int64"}, "percentageFemales": {"type": "integer", "format": "int64"}, "percentageMales": {"type": "integer", "format": "int64"}, "percentageForeigners": {"type": "integer", "format": "int64"}}}, "minaloc.mbaza.api.common.responses.GenericResponseJava.util.ListMinaloc.mbaza.api.complaint.dtos.ViewCategoryDTO.Output": {"type": "object", "properties": {"payload": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCategoryDTO.Output"}}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCategoryDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "minaloc.mbaza.api.common.responses.GenericResponseOrg.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output": {"type": "object", "properties": {"id": {"type": "string"}, "identificationNumber": {"type": "string"}, "title": {"type": "string"}, "status": {"type": "string"}, "assignedTo": {"type": "string"}, "createdFor": {"type": "string"}, "categoryNames": {"type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "urgent": {"type": "boolean"}}}, "org.springframework.data.web.PagedModelMinaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDTO.Output"}}, "page": {"$ref": "#/components/schemas/org.springframework.data.web.PagedModel.PageMetadata"}}}, "minaloc.mbaza.api.common.responses.GenericResponseMinaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.Output": {"type": "object", "properties": {"payload": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.Output"}, "message": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ContactDTO": {"type": "object", "properties": {"email": {"type": "string"}, "phone": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "nid": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.LocationDTO": {"type": "object", "properties": {"districtName": {"type": "string"}, "sectorName": {"type": "string"}, "cellName": {"type": "string"}, "villageName": {"type": "string"}, "districtId": {"type": "string"}, "sectorId": {"type": "string"}, "cellId": {"type": "string"}, "villageId": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.Output": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "location": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.LocationDTO"}, "trackingLevel": {"type": "string"}, "contact": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ContactDTO"}, "status": {"type": "string"}, "caseSeverity": {"type": "string"}, "estimatedResolutionDays": {"type": "integer", "format": "int32"}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewCategoryDTO"}, "uniqueItems": true}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewDocumentDTO"}, "uniqueItems": true}, "followUps": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewFollowUpDTO"}, "uniqueItems": true}, "primaryOrganization": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewOrganizationComplaintDTO"}, "organizations": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewOrganizationComplaintDTO"}, "uniqueItems": true}, "timeline": {"type": "array", "items": {"$ref": "#/components/schemas/minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewTimelineDTO"}, "uniqueItems": true}, "createdAt": {"type": "string", "format": "date-time"}, "urgent": {"type": "boolean"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewCategoryDTO": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewDocumentDTO": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewFollowUpDTO": {"type": "object", "properties": {"comment": {"type": "string"}, "createdByUserName": {"type": "string"}, "position": {"type": "string"}, "summary": {"type": "string"}, "documentUrls": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "createdAt": {"type": "string", "format": "date-time"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewOrganizationComplaintDTO": {"type": "object", "properties": {"organizationId": {"type": "string"}, "organizationName": {"type": "string"}}}, "minaloc.mbaza.api.complaint.dtos.ViewCaseDetailsDTO.ViewTimelineDTO": {"type": "object", "properties": {"action": {"type": "string"}, "actionMetadata": {"type": "object", "additionalProperties": {}}, "userId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}