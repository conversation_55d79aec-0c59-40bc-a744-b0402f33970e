import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApisService } from './apis.service';
import { ApisController } from './apis.controller';
import { ApisHelperService } from './apis-helper.service';
import {
  ApiDocument,
  Server,
  Tag,
  EndPoint,
  Parameter,
  RequestBody,
  Response,
  MediaType,
  Dto,
  DtoProperty
} from './entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ApiDocument,
      Server,
      Tag,
      EndPoint,
      Parameter,
      RequestBody,
      Response,
      MediaType,
      Dto,
      DtoProperty,
    ]),
  ],
  controllers: [ApisController],
  providers: [ApisService, ApisHelperService],
  exports: [ApisService],
})
export class ApisModule {}
