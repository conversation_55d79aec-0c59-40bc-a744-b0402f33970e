import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OpenApiDocDTO } from '../open-api-parser/dtos/open-api-doc.dto';
import {
  ApiDocument,
  Server,
  Tag,
  EndPoint
} from './entities';
import { ApisHelperService } from './apis-helper.service';

@Injectable()
export class ApisService {
  constructor(
    @InjectRepository(ApiDocument)
    private readonly apiDocumentRepository: Repository<ApiDocument>,
    @InjectRepository(Server)
    private readonly serverRepository: Repository<Server>,
    @InjectRepository(Tag)
    private readonly tagRepository: Repository<Tag>,
    @InjectRepository(EndPoint)
    private readonly endpointRepository: Repository<EndPoint>,
    private readonly apisHelperService: ApisHelperService,
  ) {}

  async saveApiDocument(parsedDoc: OpenApiDocDTO.ParsedApiDoc): Promise<ApiDocument> {
    // Create the main API document
    const apiDocument = this.apiDocumentRepository.create({
      title: parsedDoc.info.title,
      version: parsedDoc.info.version,
      description: parsedDoc.info.description,
    });

    // Save the API document first to get the ID
    const savedApiDocument = await this.apiDocumentRepository.save(apiDocument);

    // Create and save servers
    const servers = await this.createServers(parsedDoc.servers, savedApiDocument);
    savedApiDocument.servers = servers;

    // Create and save tags with their endpoints
    const tags = await this.createTags(parsedDoc.tags, savedApiDocument);
    savedApiDocument.tags = tags;

    return savedApiDocument;
  }

  private async createServers(
    serversData: OpenApiDocDTO.ParsedApiDoc['servers'],
    apiDocument: ApiDocument
  ): Promise<Server[]> {
    const servers = serversData.map(serverData =>
      this.serverRepository.create({
        url: serverData.url,
        description: serverData.description,
        apiDocument,
      })
    );

    return await this.serverRepository.save(servers);
  }

  private async createTags(
    tagsData: OpenApiDocDTO.ParsedTag[],
    apiDocument: ApiDocument
  ): Promise<Tag[]> {
    const tags: Tag[] = [];

    for (const tagData of tagsData) {
      const tag = this.tagRepository.create({
        name: tagData.name,
        description: tagData.description,
        apiDocument,
      });

      const savedTag = await this.tagRepository.save(tag);

      // Create endpoints for this tag
      const endpoints = await this.createEndpoints(tagData.endpoints, savedTag);
      savedTag.endpoints = endpoints;

      tags.push(savedTag);
    }

    return tags;
  }

  private async createEndpoints(
    endpointsData: OpenApiDocDTO.ParsedEndpoint[],
    tag: Tag
  ): Promise<EndPoint[]> {
    const endpoints: EndPoint[] = [];

    for (const endpointData of endpointsData) {
      const endpoint = this.endpointRepository.create({
        method: endpointData.method as any,
        path: endpointData.path,
        operationId: endpointData.operationId,
        summary: endpointData.summary,
        description: endpointData.description,
        authRequired: endpointData.auth_required,
        tag,
      });

      const savedEndpoint = await this.endpointRepository.save(endpoint);

      // Create parameters
      const parameters = await this.apisHelperService.createParameters(endpointData.parameters, savedEndpoint);
      savedEndpoint.parameters = parameters;

      // Create request body if exists
      if (endpointData.requestBody) {
        const requestBody = await this.apisHelperService.createRequestBody(endpointData.requestBody, savedEndpoint);
        savedEndpoint.requestBody = requestBody;
      }

      // Create responses
      const responses = await this.apisHelperService.createResponses(endpointData.responses, savedEndpoint);
      savedEndpoint.responses = responses;

      // Create related DTOs
      const relatedDtos = await this.apisHelperService.createDtos(endpointData.relatedDtos, savedEndpoint);
      savedEndpoint.relatedDtos = relatedDtos;

      endpoints.push(savedEndpoint);
    }

    return endpoints;
  }
}
