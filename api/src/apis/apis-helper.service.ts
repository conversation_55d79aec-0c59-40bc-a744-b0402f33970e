import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OpenApiDocDTO } from '../open-api-parser/dtos/open-api-doc.dto';
import { 
  EndPoint, 
  Parameter, 
  RequestBody, 
  Response, 
  MediaType, 
  Dto, 
  DtoProperty 
} from './entities';

@Injectable()
export class ApisHelperService {
  constructor(
    @InjectRepository(Parameter)
    private readonly parameterRepository: Repository<Parameter>,
    @InjectRepository(RequestBody)
    private readonly requestBodyRepository: Repository<RequestBody>,
    @InjectRepository(Response)
    private readonly responseRepository: Repository<Response>,
    @InjectRepository(MediaType)
    private readonly mediaTypeRepository: Repository<MediaType>,
    @InjectRepository(Dto)
    private readonly dtoRepository: Repository<Dto>,
    @InjectRepository(DtoProperty)
    private readonly dtoPropertyRepository: Repository<DtoProperty>,
  ) {}

  async createParameters(
    parametersData: OpenApiDocDTO.ParsedParameter[], 
    endpoint: EndPoint
  ): Promise<Parameter[]> {
    const parameters = parametersData.map(paramData => 
      this.parameterRepository.create({
        name: paramData.name,
        in: paramData.in as any,
        required: paramData.required,
        schema: paramData.schema,
        description: paramData.description,
        endpoint,
      })
    );

    return await this.parameterRepository.save(parameters);
  }

  async createRequestBody(
    requestBodyData: OpenApiDocDTO.ParsedRequestBody, 
    endpoint: EndPoint
  ): Promise<RequestBody> {
    const requestBody = this.requestBodyRepository.create({
      required: requestBodyData.required,
      endpoint,
    });

    const savedRequestBody = await this.requestBodyRepository.save(requestBody);

    // Create media types for request body
    const mediaTypes = await this.createMediaTypesForRequestBody(
      requestBodyData.content, 
      savedRequestBody
    );
    savedRequestBody.content = mediaTypes;

    return savedRequestBody;
  }

  async createResponses(
    responsesData: OpenApiDocDTO.ParsedResponse[], 
    endpoint: EndPoint
  ): Promise<Response[]> {
    const responses: Response[] = [];

    for (const responseData of responsesData) {
      const response = this.responseRepository.create({
        statusCode: responseData.statusCode,
        description: responseData.description,
        endpoint,
      });

      const savedResponse = await this.responseRepository.save(response);

      // Create media types for response if content exists
      if (responseData.content) {
        const mediaTypes = await this.createMediaTypesForResponse(
          responseData.content, 
          savedResponse
        );
        savedResponse.content = mediaTypes;
      }

      responses.push(savedResponse);
    }

    return responses;
  }

  async createDtos(
    dtosData: OpenApiDocDTO.ParsedDTO[], 
    endpoint: EndPoint
  ): Promise<Dto[]> {
    const dtos: Dto[] = [];

    for (const dtoData of dtosData) {
      const dto = this.dtoRepository.create({
        name: dtoData.name,
        type: dtoData.type as any,
        description: dtoData.description,
        required: dtoData.required,
        items: dtoData.items,
        endpoint,
      });

      const savedDto = await this.dtoRepository.save(dto);

      // Create properties for this DTO if they exist
      if (dtoData.properties) {
        const properties = await this.createDtoProperties(dtoData.properties, savedDto);
        savedDto.properties = properties;
      }

      dtos.push(savedDto);
    }

    return dtos;
  }

  private async createMediaTypesForRequestBody(
    contentData: Record<string, OpenApiDocDTO.ParsedMediaType>, 
    requestBody: RequestBody
  ): Promise<MediaType[]> {
    const mediaTypes = Object.entries(contentData).map(([mediaType, mediaTypeData]) => 
      this.mediaTypeRepository.create({
        mediaType,
        schema: mediaTypeData.schema,
        requestBody,
      })
    );

    return await this.mediaTypeRepository.save(mediaTypes);
  }

  private async createMediaTypesForResponse(
    contentData: Record<string, OpenApiDocDTO.ParsedMediaType>, 
    response: Response
  ): Promise<MediaType[]> {
    const mediaTypes = Object.entries(contentData).map(([mediaType, mediaTypeData]) => 
      this.mediaTypeRepository.create({
        mediaType,
        schema: mediaTypeData.schema,
        response,
      })
    );

    return await this.mediaTypeRepository.save(mediaTypes);
  }

  private async createDtoProperties(
    propertiesData: Record<string, OpenApiDocDTO.ParsedProperty>, 
    dto: Dto
  ): Promise<DtoProperty[]> {
    const properties = Object.entries(propertiesData).map(([name, propertyData]) => 
      this.dtoPropertyRepository.create({
        name,
        type: propertyData.type,
        format: propertyData.format,
        description: propertyData.description,
        enum: propertyData.enum,
        items: propertyData.items,
        ref: propertyData.$ref,
        minLength: propertyData.minLength,
        maxLength: propertyData.maxLength,
        minimum: propertyData.minimum,
        maximum: propertyData.maximum,
        pattern: propertyData.pattern,
        dto,
      })
    );

    return await this.dtoPropertyRepository.save(properties);
  }
}
