# APIs Module - OpenAPI Document Management

This module handles saving and managing parsed OpenAPI documents in the database with a hierarchical structure.

## Workflow Options

### Option 1: Direct Upload and Save (One-Step Process)
**Endpoint:** `POST /parser/upload-and-save`

Upload a Swagger/OpenAPI file and it will be parsed and saved in one operation.

```bash
curl -X POST \
  http://localhost:3000/parser/upload-and-save \
  -H 'Content-Type: multipart/form-data' \
  -F 'swaggerDoc=@your-swagger-file.json'
```

### Option 2: Parse Then Save (Two-Step Process)
**Step 1:** `POST /parser/upload` - Parse only
**Step 2:** `POST /apis/save` - Save parsed data

```bash
# Step 1: Parse the file
curl -X POST \
  http://localhost:3000/parser/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'swaggerDoc=@your-swagger-file.json'

# Step 2: Save the parsed data (use the response from step 1)
curl -X POST \
  http://localhost:3000/apis/save \
  -H 'Content-Type: application/json' \
  -d '{ "parsed_data_from_step_1" }'
```

### Option 3: Parse and Send to APIs Module
**Endpoint:** `POST /parser/parse-and-send`

Upload a file, parse it, and explicitly send it to the APIs module for saving.

```bash
curl -X POST \
  http://localhost:3000/parser/parse-and-send \
  -H 'Content-Type: multipart/form-data' \
  -F 'swaggerDoc=@your-swagger-file.json'
```

### Option 4: Send Already Parsed Data
**Endpoint:** `POST /parser/send-parsed-data`

If you already have parsed OpenAPI data, send it directly to be saved.

```bash
curl -X POST \
  http://localhost:3000/parser/send-parsed-data \
  -H 'Content-Type: application/json' \
  -d '{ "your_parsed_openapi_data" }'
```

## Retrieving Saved APIs

### Get All API Documents
```bash
curl -X GET http://localhost:3000/apis
```

### Get Specific API Document
```bash
curl -X GET http://localhost:3000/apis/{api-document-id}
```

## Database Structure

The parsed OpenAPI data is saved in a hierarchical structure:

```
ApiDocument (root)
├── Servers[]
└── Tags[]
    └── Endpoints[]
        ├── Parameters[]
        ├── RequestBody
        │   └── MediaTypes[]
        ├── Responses[]
        │   └── MediaTypes[]
        └── RelatedDtos[]
            └── DtoProperties[]
```

## Key Features

✅ **Hierarchical Structure**: Tags contain endpoints, endpoints contain DTOs
✅ **Complete DTO Resolution**: All nested DTO references are resolved
✅ **Flexible Workflows**: Multiple ways to parse and save data
✅ **Full Data Preservation**: All OpenAPI specification data is maintained
✅ **Easy Querying**: Proper database relationships for efficient queries

## Response Format

All endpoints return data in this format:

```json
{
  "message": "Operation successful",
  "payload": {
    "apiDocumentId": "uuid",
    "title": "API Title",
    "version": "1.0.0",
    "tagsCount": 5,
    "totalEndpoints": 25,
    "totalDtos": 15
  }
}
```
