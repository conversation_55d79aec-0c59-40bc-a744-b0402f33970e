import { Column, Entity, ManyToOne } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { RequestBody } from "./request-body.entity";
import { Response } from "./response.entity";

@Entity('media_types')
export class MediaType extends AbstractEntity {
    @Column()
    mediaType: string; // e.g., 'application/json', 'text/plain'

    @Column('jsonb')
    schema: any; // The schema definition

    @ManyToOne(() => RequestBody, requestBody => requestBody.content, { nullable: true })
    requestBody?: RequestBody;

    @ManyToOne(() => Response, response => response.content, { nullable: true })
    response?: Response;
}
