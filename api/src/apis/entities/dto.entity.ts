import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { ValueType } from "../enums/value-type.enum";
import { EndPoint } from "./endpoint.entity";
import { DtoProperty } from "./dto-property.entity";

@Entity('dtos')
export class Dto extends AbstractEntity {
    @Column()
    name: string;

    @Column({ type: 'enum', enum: ValueType })
    type: ValueType;

    @Column({ nullable: true })
    description?: string;

    @Column('simple-array', { nullable: true })
    required?: string[];

    @Column('jsonb', { nullable: true })
    items?: any; // For array type DTOs

    @OneToMany(() => DtoProperty, property => property.dto, { cascade: true })
    properties: DtoProperty[];

    @ManyToOne(() => EndPoint, endpoint => endpoint.relatedDtos)
    endpoint: EndPoint;
}
