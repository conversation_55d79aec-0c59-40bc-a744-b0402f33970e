import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToOne } from "typeorm";
import { HttpMethod } from "../enums/http-methods.enum";
import { Tag } from "./tag.entity";
import { Schema } from "./schema.entity";
import { Parameter } from "./parameters.entity";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";

@Entity('endpoints')
export class EndPoint extends AbstractEntity{
    @Column()
    method: HttpMethod;

    @Column()
    path: string;

    @Column()
    authRequired: boolean;

    @Column()
    description: string;

    @ManyToOne(() => Tag, tag => tag.endpoints)
    tag: Tag;

    @OneToOne(() => Schema, schema => schema.endpoint)
    schema: Schema;

    @ManyToOne(() => EndPoint, endpoint => endpoint.parameters)
    parameters: Parameter[];
}