import { BaseEntity, Column, Entity, ManyToOne, OneToOne } from "typeorm";
import { HttpMethod } from "../enums/http-methods.enum";
import { Tag } from "./tag.entity";
import { Schema } from "./schema.entity";
import { Parameter } from "./parameters.entity";

@Entity('endpoints')
export class EndPoint extends BaseEntity{
    @Column()
    method: HttpMethod;

    @Column()
    path: string;

    @Column()
    authRequired: boolean;

    @Column()
    description: string;

    @ManyToOne(() => Tag, tag => tag.endpoints, { eager: true })
    tag: Tag;

    @OneToOne(() => Schema, schema => schema.endpoint, { eager: true })
    schema: Schema;

    @ManyToOne(() => EndPoint, endpoint => endpoint.parameters, { eager: true })
    parameters: Parameter[];
}