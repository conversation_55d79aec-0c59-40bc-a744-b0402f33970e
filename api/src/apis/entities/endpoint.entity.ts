import { Column, <PERSON><PERSON><PERSON>, ManyToOne, OneToOne, OneToMany } from "typeorm";
import { HttpMethod } from "../enums/http-methods.enum";
import { Tag } from "./tag.entity";
import { Parameter } from "./parameters.entity";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { RequestBody } from "./request-body.entity";
import { Response } from "./response.entity";
import { Dto } from "./dto.entity";

@Entity('endpoints')
export class EndPoint extends AbstractEntity{
    @Column({ type: 'enum', enum: HttpMethod })
    method: HttpMethod;

    @Column()
    path: string;

    @Column({ nullable: true })
    operationId?: string;

    @Column({ nullable: true })
    summary?: string;

    @Column({ nullable: true })
    description?: string;

    @Column({ default: false })
    authRequired: boolean;

    @ManyToOne(() => Tag, tag => tag.endpoints)
    tag: Tag;

    @OneToMany(() => Parameter, parameter => parameter.endpoint, { cascade: true })
    parameters: Parameter[];

    @OneToOne(() => RequestBody, requestBody => requestBody.endpoint, { cascade: true })
    requestBody?: RequestBody;

    @OneToMany(() => Response, response => response.endpoint, { cascade: true })
    responses: Response[];

    @OneToMany(() => Dto, dto => dto.endpoint, { cascade: true })
    relatedDtos: Dto[];
}