import { <PERSON>umn, <PERSON><PERSON><PERSON>, OneTo<PERSON>ne, OneToMany } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { EndPoint } from "./endpoint.entity";
import { MediaType } from "./media-type.entity";

@Entity('request_bodies')
export class RequestBody extends AbstractEntity {
    @Column({ default: false })
    required: boolean;

    @OneToMany(() => MediaType, mediaType => mediaType.requestBody, { cascade: true })
    content: MediaType[];

    @OneToOne(() => EndPoint, endpoint => endpoint.requestBody)
    endpoint: EndPoint;
}
