import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, OneToMany } from "typeorm";
import { EndPoint } from "./endpoint.entity";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { ApiDocument } from "./api-document.entity";

@Entity('tags')
export class Tag extends AbstractEntity{
    @Column()
    name: string;

    @Column({ nullable: true })
    description?: string;

    @OneToMany(() => EndPoint, endpoint => endpoint.tag, { cascade: true })
    endpoints: EndPoint[];

    @ManyToOne(() => ApiDocument, apiDocument => apiDocument.tags)
    apiDocument: ApiDocument;
}