import { BaseEntity, <PERSON>umn, <PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { EndPoint } from "./endpoint.entity";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";

@Entity('tags')
export class Tag extends AbstractEntity{
    @Column()
    name: string;

    @ManyToOne(() => EndPoint, endpoint => endpoint.tag, { nullable: true })
    endpoints: EndPoint[];
}