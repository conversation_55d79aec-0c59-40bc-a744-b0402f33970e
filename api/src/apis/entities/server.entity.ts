import { Column, Entity, <PERSON>ToOne } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { ApiDocument } from "./api-document.entity";

@Entity('servers')
export class Server extends AbstractEntity {
    @Column()
    url: string;

    @Column({ nullable: true })
    description?: string;

    @ManyToOne(() => ApiDocument, apiDocument => apiDocument.servers)
    apiDocument: ApiDocument;
}
