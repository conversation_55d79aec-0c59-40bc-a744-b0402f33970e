import { Column, <PERSON>tity, OneToMany } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { Server } from "./server.entity";
import { Tag } from "./tag.entity";

@Entity('api_documents')
export class ApiDocument extends AbstractEntity {
    @Column()
    title: string;

    @Column()
    version: string;

    @Column({ nullable: true })
    description?: string;

    @OneToMany(() => Server, server => server.apiDocument, { cascade: true })
    servers: Server[];

    @OneToMany(() => Tag, tag => tag.apiDocument, { cascade: true })
    tags: Tag[];
}
