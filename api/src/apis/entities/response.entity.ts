import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { EndPoint } from "./endpoint.entity";
import { MediaType } from "./media-type.entity";

@Entity('responses')
export class Response extends AbstractEntity {
    @Column()
    statusCode: string;

    @Column({ nullable: true })
    description?: string;

    @OneToMany(() => MediaType, mediaType => mediaType.response, { cascade: true })
    content: MediaType[];

    @ManyToOne(() => EndPoint, endpoint => endpoint.responses)
    endpoint: EndPoint;
}
