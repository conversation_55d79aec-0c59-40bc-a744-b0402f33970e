import { <PERSON>umn, <PERSON>To<PERSON>ne, OneToOne } from "typeorm";
import { Entity } from "typeorm/decorator/entity/Entity";
import { EndPoint } from "./endpoint.entity";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";

@Entity('schemas')
export class Schema extends  AbstractEntity{
    // @Column()
    // request: JSON;

    // @Column()
    // response: JSON;

    @OneToOne(() => EndPoint, endpoint => endpoint.schema)
    endpoint: EndPoint;
}