import { Column, Entity, ManyToOne } from "typeorm";
import { EndPoint } from "./endpoint.entity";
import { PlacementType } from "../enums/placement-type.enum";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";

@Entity('parameters')
export class Parameter extends AbstractEntity {
    @Column()
    name: string;

    @Column({ type: 'enum', enum: PlacementType })
    in: PlacementType; // Changed from placement_type to match OpenAPI spec

    @Column({ default: false })
    required: boolean;

    @Column('jsonb')
    schema: any; // Store the parameter schema as JSON

    @Column({ nullable: true })
    description?: string;

    @ManyToOne(() => EndPoint, endpoint => endpoint.parameters)
    endpoint: EndPoint;
}