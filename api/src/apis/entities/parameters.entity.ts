import { Column, <PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { EndPoint } from "./endpoint.entity";
import { ValueType } from "../enums/value-type.enum";
import { PlacementType } from "../enums/placement-type.enum";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";

@Entity('parameters')
export class Parameter extends AbstractEntity {
    @Column()
    name: string;

    @Column()
    placement_type: PlacementType;

    @Column()
    type: ValueType;

    @Column({ default: false })
    required: boolean;

    @ManyToOne(() => EndPoint, endpoint => endpoint.parameters)
    endpoint: EndPoint;
}