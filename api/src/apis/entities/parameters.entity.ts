import { Base<PERSON>ntity, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { EndPoint } from "./endpoint.entity";
import { ValueType } from "../enums/value-type.enum";
import { PlacementType } from "../enums/placement-type.enum";

@Entity('parameters')
export class Parameter extends BaseEntity {
    @Column()
    name: string;

    @Column()
    placement_type: PlacementType;

    @Column()
    type: ValueType;

    @Column({ default: false })
    required: boolean;

    @ManyToOne(() => EndPoint, endpoint => endpoint.parameters, { eager: true })
    endpoint: EndPoint;
}