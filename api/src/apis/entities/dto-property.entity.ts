import { Column, Entity, ManyToOne } from "typeorm";
import { AbstractEntity } from "src/__shared__/entities/abstract.entity";
import { Dto } from "./dto.entity";

@Entity('dto_properties')
export class DtoProperty extends AbstractEntity {
    @Column()
    name: string;

    @Column()
    type: string;

    @Column({ nullable: true })
    format?: string;

    @Column({ nullable: true })
    description?: string;

    @Column('simple-array', { nullable: true })
    enum?: any[];

    @Column('jsonb', { nullable: true })
    items?: any; // For array properties

    @Column({ nullable: true })
    ref?: string; // For $ref properties

    @Column({ nullable: true })
    minLength?: number;

    @Column({ nullable: true })
    maxLength?: number;

    @Column({ nullable: true })
    minimum?: number;

    @Column({ nullable: true })
    maximum?: number;

    @Column({ nullable: true })
    pattern?: string;

    @ManyToOne(() => Dto, dto => dto.properties)
    dto: Dto;
}
