import { Controller, Post, Body, Get, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ApisService } from './apis.service';
import { OpenApiDocDTO } from '../open-api-parser/dtos/open-api-doc.dto';
import { GenericResponse } from 'src/__shared__/dto/generic-response.dto';
import { OkResponse, PostOperation, GetOperation, DeleteOperation } from 'src/__shared__/decorators';

@Controller('apis')
@ApiTags('APIs Management')
export class ApisController {
  constructor(private readonly apisService: ApisService) {}

  @PostOperation('save', 'Save parsed OpenAPI document to database')
  @OkResponse()
  async saveApiDocument(@Body() parsedDoc: OpenApiDocDTO.ParsedApiDoc) {
    const savedApiDocument = await this.apisService.saveApiDocument(parsedDoc);

    return new GenericResponse('API document saved successfully', {
      apiDocumentId: savedApiDocument.id,
      title: savedApiDocument.title,
      version: savedApiDocument.version,
      description: savedApiDocument.description,
      serversCount: savedApiDocument.servers.length,
      tagsCount: savedApiDocument.tags.length,
      totalEndpoints: savedApiDocument.tags.reduce(
        (total, tag) => total + tag.endpoints.length,
        0
      ),
      totalDtos: savedApiDocument.tags.reduce(
        (total, tag) => total + tag.endpoints.reduce(
          (endpointTotal, endpoint) => endpointTotal + endpoint.relatedDtos.length,
          0
        ),
        0
      ),
    });
  }

  @GetOperation(':id', 'Get API document by ID')
  @OkResponse()
  async getApiDocument(@Param('id') id: string) {
    const apiDocument = await this.apisService.getApiDocument(id);
    return new GenericResponse('API document retrieved successfully', apiDocument);
  }

  @GetOperation('', 'Get all API documents')
  @OkResponse()
  async getAllApiDocuments() {
    const apiDocuments = await this.apisService.getAllApiDocuments();
    return new GenericResponse('API documents retrieved successfully', apiDocuments);
  }

  @DeleteOperation(':id', 'Delete API document by ID')
  @OkResponse()
  async deleteApiDocument(@Param('id') id: string) {
    await this.apisService.deleteApiDocument(id);
    return new GenericResponse('API document deleted successfully', { deletedId: id });
  }
}
