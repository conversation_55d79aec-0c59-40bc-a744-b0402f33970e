import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { OpenApiFile } from "./entities/open-api-file.entity";
import { Repository } from "typeorm";
import { OpenApiDocDTO } from "./dtos/open-api-doc.dto";
import { ApisService } from "../apis/apis.service";

@Injectable()
export class OpenApiParserService {
  constructor(
    @InjectRepository(OpenApiFile)
    private readonly openApiFileRepository: Repository<OpenApiFile>,
    private readonly apisService: ApisService,
  ) {}

  async parseOpenApiDoc(swaggerDoc: Express.Multer.File) {
    const openApiDoc = JSON.parse(
      swaggerDoc.buffer.toString("utf8"),
    ) as any;

    const parsedDoc = this.parseOpenApiJson(openApiDoc);
    return parsedDoc;
  }

  async parseAndSaveOpenApiDoc(swaggerDoc: Express.Multer.File) {
    const parsedDoc = await this.parseOpenApiDoc(swaggerDoc);
    const savedApiDocument = await this.apisService.saveApiDocument(parsedDoc);
    return {
      parsedDoc,
      savedApiDocument,
    };
  }

  private parseOpenApiJson(doc: any): OpenApiDocDTO.ParsedApiDoc {
    const allDtos = this.parseDTOs(doc.components?.schemas || {});
    const endpoints = this.parseEndpoints(
      doc.paths || {},
      doc.components?.securitySchemes,
      allDtos,
    );

    return {
      info: this.parseInfo(doc.info),
      servers: this.parseServers(doc.servers || []),
      tags: this.parseTagsWithEndpoints(doc.tags || [], endpoints),
    };
  }

  private parseInfo(info: any): OpenApiDocDTO.ParsedApiDoc["info"] {
    return {
      title: info?.title || "Unknown API",
      version: info?.version || "1.0.0",
      description: info?.description,
    };
  }

  private parseServers(servers: any[]): OpenApiDocDTO.ParsedApiDoc["servers"] {
    return servers.map((server) => ({
      url: server.url,
      description: server.description,
    }));
  }

  private parseTagsWithEndpoints(
    tags: any[],
    endpoints: Array<OpenApiDocDTO.ParsedEndpoint & { tag: string }>,
  ): OpenApiDocDTO.ParsedTag[] {
    // Create a map of tag names to their definitions
    const tagDefinitions = new Map<string, { name: string; description?: string }>();

    // Add explicitly defined tags
    tags.forEach((tag) => {
      tagDefinitions.set(tag.name, {
        name: tag.name,
        description: tag.description,
      });
    });

    // Group endpoints by tag
    const endpointsByTag = new Map<string, OpenApiDocDTO.ParsedEndpoint[]>();

    endpoints.forEach((endpoint) => {
      const tagName = endpoint.tag;

      // Ensure tag definition exists (create default if not)
      if (!tagDefinitions.has(tagName)) {
        tagDefinitions.set(tagName, {
          name: tagName,
          description: undefined,
        });
      }

      // Add endpoint to tag group (remove the tag property from endpoint)
      const { tag, ...endpointWithoutTag } = endpoint;
      if (!endpointsByTag.has(tagName)) {
        endpointsByTag.set(tagName, []);
      }
      endpointsByTag.get(tagName)!.push(endpointWithoutTag);
    });

    // Create final tag structure
    return Array.from(tagDefinitions.values()).map((tagDef) => ({
      name: tagDef.name,
      description: tagDef.description,
      endpoints: endpointsByTag.get(tagDef.name) || [],
    }));
  }

  private findRelatedDtos(
    operation: any,
    allDtos: OpenApiDocDTO.ParsedDTO[],
  ): OpenApiDocDTO.ParsedDTO[] {
    const relatedDtoNames = new Set<string>();

    // Extract DTO references from request body
    if (operation.requestBody?.content) {
      Object.values(operation.requestBody.content).forEach((mediaType: any) => {
        this.extractDtoReferences(mediaType.schema, relatedDtoNames);
      });
    }

    // Extract DTO references from responses
    if (operation.responses) {
      Object.values(operation.responses).forEach((response: any) => {
        if (response.content) {
          Object.values(response.content).forEach((mediaType: any) => {
            this.extractDtoReferences(mediaType.schema, relatedDtoNames);
          });
        }
      });
    }

    // Extract DTO references from parameters
    if (operation.parameters) {
      operation.parameters.forEach((param: any) => {
        this.extractDtoReferences(param.schema, relatedDtoNames);
      });
    }

    // Recursively resolve all nested DTO references
    this.resolveNestedDtoReferences(relatedDtoNames, allDtos);

    // Return the actual DTO objects that match the references
    return allDtos.filter((dto) => relatedDtoNames.has(dto.name));
  }

  private extractDtoReferences(schema: any, dtoNames: Set<string>): void {
    if (!schema) return;

    // Direct reference
    if (schema.$ref) {
      const refName = schema.$ref.split('/').pop();
      if (refName) {
        dtoNames.add(refName);
      }
    }

    // Array items
    if (schema.items) {
      this.extractDtoReferences(schema.items, dtoNames);
    }

    // Object properties
    if (schema.properties) {
      Object.values(schema.properties).forEach((prop: any) => {
        this.extractDtoReferences(prop, dtoNames);
      });
    }

    // AllOf, OneOf, AnyOf
    ['allOf', 'oneOf', 'anyOf'].forEach((key) => {
      if (schema[key] && Array.isArray(schema[key])) {
        schema[key].forEach((subSchema: any) => {
          this.extractDtoReferences(subSchema, dtoNames);
        });
      }
    });
  }

  private resolveNestedDtoReferences(
    dtoNames: Set<string>,
    allDtos: OpenApiDocDTO.ParsedDTO[],
  ): void {
    const processedDtos = new Set<string>();
    const dtosToProcess = Array.from(dtoNames);

    while (dtosToProcess.length > 0) {
      const currentDtoName = dtosToProcess.pop()!;

      if (processedDtos.has(currentDtoName)) {
        continue;
      }

      processedDtos.add(currentDtoName);

      // Find the DTO definition
      const dto = allDtos.find(d => d.name === currentDtoName);
      if (!dto) {
        continue;
      }

      // Extract references from this DTO's properties
      const nestedRefs = new Set<string>();

      if (dto.properties) {
        Object.values(dto.properties).forEach((property) => {
          this.extractDtoReferencesFromProperty(property, nestedRefs);
        });
      }

      if (dto.items) {
        this.extractDtoReferences(dto.items, nestedRefs);
      }

      // Add newly found references to the main set and processing queue
      nestedRefs.forEach((refName) => {
        if (!dtoNames.has(refName)) {
          dtoNames.add(refName);
          dtosToProcess.push(refName);
        }
      });
    }
  }

  private extractDtoReferencesFromProperty(
    property: OpenApiDocDTO.ParsedProperty,
    dtoNames: Set<string>,
  ): void {
    // Direct reference
    if (property.$ref) {
      const refName = property.$ref.split('/').pop();
      if (refName) {
        dtoNames.add(refName);
      }
    }

    // Array items
    if (property.items) {
      this.extractDtoReferences(property.items, dtoNames);
    }
  }

  private parseEndpoints(
    paths: any,
    securitySchemes?: any,
    allDtos?: OpenApiDocDTO.ParsedDTO[],
  ): Array<OpenApiDocDTO.ParsedEndpoint & { tag: string }> {
    const endpoints: Array<OpenApiDocDTO.ParsedEndpoint & { tag: string }> = [];

    for (const [path, pathItem] of Object.entries(paths)) {
      const httpMethods = [
        "get",
        "post",
        "put",
        "patch",
        "delete",
        "head",
        "options",
        "trace",
      ];

      for (const method of httpMethods) {
        const operation = (pathItem as any)[method];
        if (!operation) continue;

        const relatedDtos = this.findRelatedDtos(operation, allDtos || []);

        const endpoint: OpenApiDocDTO.ParsedEndpoint & { tag: string } = {
          method: method.toUpperCase(),
          path,
          tag: operation.tags?.[0] || "default",
          operationId: operation.operationId,
          summary: operation.summary,
          description: operation.description,
          auth_required: this.isAuthRequired(
            operation.security,
            securitySchemes,
          ),
          parameters: this.parseParameters(operation.parameters || []),
          requestBody: operation.requestBody
            ? this.parseRequestBody(operation.requestBody)
            : undefined,
          responses: this.parseResponses(operation.responses || {}),
          relatedDtos,
        };

        endpoints.push(endpoint);
      }
    }

    return endpoints;
  }

  private isAuthRequired(security?: any[], securitySchemes?: any): boolean {
    if (!security || security.length === 0) return false;

    // Check if any security requirement is defined
    return security.some(
      (securityRequirement) => Object.keys(securityRequirement).length > 0,
    );
  }

  private parseParameters(parameters: any[]): OpenApiDocDTO.ParsedParameter[] {
    return parameters.map((param) => ({
      name: param.name,
      in: param.in,
      required: param.required || false,
      schema: param.schema,
      description: param.description,
    }));
  }

  private parseRequestBody(requestBody: any): OpenApiDocDTO.ParsedRequestBody {
    const content: Record<string, OpenApiDocDTO.ParsedMediaType> = {};

    if (requestBody.content) {
      for (const [mediaType, mediaTypeObj] of Object.entries(
        requestBody.content,
      )) {
        content[mediaType] = {
          schema: (mediaTypeObj as any).schema,
        };
      }
    }

    return {
      required: requestBody.required || false,
      content,
    };
  }

  private parseResponses(responses: any): OpenApiDocDTO.ParsedResponse[] {
    const parsedResponses: OpenApiDocDTO.ParsedResponse[] = [];

    for (const [statusCode, response] of Object.entries(responses)) {
      const content: Record<string, OpenApiDocDTO.ParsedMediaType> = {};

      if ((response as any).content) {
        for (const [mediaType, mediaTypeObj] of Object.entries(
          (response as any).content,
        )) {
          content[mediaType] = {
            schema: (mediaTypeObj as any).schema,
          };
        }
      }

      parsedResponses.push({
        statusCode,
        description: (response as any).description,
        content: Object.keys(content).length > 0 ? content : undefined,
      });
    }

    return parsedResponses;
  }

  private parseDTOs(schemas: any): OpenApiDocDTO.ParsedDTO[] {
    const dtos: OpenApiDocDTO.ParsedDTO[] = [];

    for (const [name, schema] of Object.entries(schemas)) {
      const schemaObj = schema as any;

      const dto: OpenApiDocDTO.ParsedDTO = {
        name,
        type: this.getSchemaType(schemaObj),
        description: schemaObj.description,
      };

      if (schemaObj.type === "object" && schemaObj.properties) {
        dto.properties = this.parseProperties(schemaObj.properties);
        dto.required = schemaObj.required || [];
      }

      if (schemaObj.type === "array" && schemaObj.items) {
        dto.items = schemaObj.items;
      }

      dtos.push(dto);
    }

    return dtos;
  }

  private getSchemaType(schema: any): OpenApiDocDTO.ParsedDTO["type"] {
    if (schema.type) {
      return schema.type === "integer" ? "number" : schema.type;
    }
    if (schema.properties) {
      return "object";
    }
    if (schema.items) {
      return "array";
    }
    return "object"; // default
  }

  private parseProperties(
    properties: any,
  ): Record<string, OpenApiDocDTO.ParsedProperty> {
    const parsedProperties: Record<string, OpenApiDocDTO.ParsedProperty> = {};

    for (const [propName, propSchema] of Object.entries(properties)) {
      const prop = propSchema as any;

      parsedProperties[propName] = {
        type: prop.type === "integer" ? "number" : prop.type || "string",
        format: prop.format,
        description: prop.description,
        enum: prop.enum,
        items: prop.items,
        $ref: prop.$ref,
        minLength: prop.minLength,
        maxLength: prop.maxLength,
        minimum: prop.minimum,
        maximum: prop.maximum,
        pattern: prop.pattern,
      };
    }

    return parsedProperties;
  }
}
