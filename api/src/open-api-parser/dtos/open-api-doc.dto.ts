export namespace OpenApiDocDTO {
  export interface ParsedParameter {
    name: string;
    in: "query" | "path" | "header" | "cookie";
    required: boolean;
    schema: any;
    description?: string;
  }

  export interface ParsedRequestBody {
    required: boolean;
    content: Record<string, ParsedMediaType>;
  }

  export interface ParsedMediaType {
    schema: any;
  }

  export interface ParsedResponse {
    statusCode: string;
    description?: string;
    content?: Record<string, ParsedMediaType>;
  }

  export interface ParsedDTO {
    name: string;
    type: "object" | "array" | "string" | "number" | "boolean";
    properties?: Record<string, ParsedProperty>;
    required?: string[];
    items?: any;
    description?: string;
  }

  export interface ParsedProperty {
    type: string;
    format?: string;
    description?: string;
    required?: boolean;
    enum?: any[];
    items?: any; // For array properties
    $ref?: string;
    minLength?: number;
    maxLength?: number;
    minimum?: number;
    maximum?: number;
    pattern?: string;
  }

  export interface ParsedEndpoint {
    method: string;
    path: string;
    operationId?: string;
    summary?: string;
    description?: string;
    auth_required: boolean;
    parameters: ParsedParameter[];
    requestBody?: ParsedRequestBody;
    responses: ParsedResponse[];
    // DTOs related to this endpoint (from request/response schemas)
    relatedDtos: ParsedDTO[];
  }

  export interface ParsedTag {
    name: string;
    description?: string;
    endpoints: ParsedEndpoint[];
  }

  export interface ParsedApiDoc {
    info: {
      title: string;
      version: string;
      description?: string;
    };
    servers: Array<{
      url: string;
      description?: string;
    }>;
    tags: ParsedTag[];
  }

  export class Output {
    parsedDoc: ParsedApiDoc;
  }
}
