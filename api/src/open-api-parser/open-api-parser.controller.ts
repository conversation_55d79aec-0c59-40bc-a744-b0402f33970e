import { Controller, UploadedFile, UseInterceptors } from "@nestjs/common";
import { OpenApiParserService } from "./open-api-parser.service";
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from "@nestjs/swagger";
import { OkResponse, PostOperation } from "src/__shared__/decorators";
import { FileInterceptor } from "@nestjs/platform-express";
import { GenericResponse } from "src/__shared__/dto/generic-response.dto";

@Controller("parser")
@ApiTags("Open Api Parser controller")
export class OpenApiParserController {
  constructor(private readonly service: OpenApiParserService) {}

  @PostOperation("upload", "Upload swagger documentation")
  @OkResponse()
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("swaggerDoc"))
  @ApiBody({
    description: "Upload Swagger file",
    required: true,
    schema: {
      type: "object",
      properties: {
        swaggerDoc: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  async uploadFile(@UploadedFile() swaggerDoc: Express.Multer.File) {
    const payload = await this.service.parseOpenApiDoc(swaggerDoc);
    return new GenericResponse("File uploaded successfully", payload);
  }

  @PostOperation("upload-and-save", "Upload and save swagger documentation to database")
  @OkResponse()
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("swaggerDoc"))
  @ApiBody({
    description: "Upload and save Swagger file to database",
    required: true,
    schema: {
      type: "object",
      properties: {
        swaggerDoc: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  async uploadAndSaveFile(@UploadedFile() swaggerDoc: Express.Multer.File) {
    const result = await this.service.parseAndSaveOpenApiDoc(swaggerDoc);
    return new GenericResponse("File uploaded and saved successfully", {
      apiDocumentId: result.savedApiDocument.id,
      title: result.savedApiDocument.title,
      version: result.savedApiDocument.version,
      tagsCount: result.savedApiDocument.tags.length,
      totalEndpoints: result.savedApiDocument.tags.reduce(
        (total, tag) => total + tag.endpoints.length,
        0
      ),
    });
  }
}
