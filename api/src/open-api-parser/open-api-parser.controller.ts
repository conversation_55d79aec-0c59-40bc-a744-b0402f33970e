import { Controller, UploadedFile, UseInterceptors } from "@nestjs/common";
import { OpenApiParserService } from "./open-api-parser.service";
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from "@nestjs/swagger";
import { OkResponse, PostOperation } from "src/__shared__/decorators";
import { FileInterceptor } from "@nestjs/platform-express";
import { GenericResponse } from "src/__shared__/dto/generic-response.dto";

@Controller("parser")
@ApiTags("Open Api Parser controller")
export class OpenApiParserController {
  constructor(private readonly service: OpenApiParserService) {}

  @PostOperation("upload", "Upload swagger documentation")
  @OkResponse()
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("swaggerDoc"))
  @ApiBody({
    description: "Upload Swagger file",
    required: true,
    schema: {
      type: "object",
      properties: {
        swaggerDoc: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  async uploadFile(@UploadedFile() swaggerDoc: Express.Multer.File) {
    const payload = await this.service.parseOpenApiDoc(swaggerDoc);
    return new GenericResponse("File uploaded successfully", payload);
  }
}
