import { Module } from "@nestjs/common";
import { OpenApiParserService } from "./open-api-parser.service";
import { OpenApiParserController } from "./open-api-parser.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { OpenApiFile } from "./entities/open-api-file.entity";

@Module({
  imports: [TypeOrmModule.forFeature([OpenApiFile])],
  controllers: [OpenApiParserController],
  providers: [OpenApiParserService],
})
export class OpenApiParserModule {}
