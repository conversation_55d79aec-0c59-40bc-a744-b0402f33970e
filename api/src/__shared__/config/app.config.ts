import { INestApplication, UnauthorizedException } from "@nestjs/common";
import { CorsOptions } from "@nestjs/common/interfaces/external/cors-options.interface";
import { ConfigService } from "@nestjs/config";
import { RuntimeException } from "@nestjs/core/errors/exceptions";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import * as cookieParser from "cookie-parser";
import helmet from "helmet";
import { IAppConfig } from "../interfaces/app-config.interface";

/**
 * Defines the application config variables
 * @returns the Application config variables
 */
export function appConfig(): IAppConfig {
  validateEnvVariables();

  return {
    port: +process.env.PORT,
    database: {
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      host: process.env.DB_HOST,
      port: +process.env.DB_PORT,
    },
    env: process.env.NODE_ENV,
    swaggerEnabled: process.env.SWAGGER_ENABLED === "true",
  };
}

function validateEnvVariables(): void {
  const requiredEnvVars = [
    "PORT",
    "DB_USERNAME",
    "DB_PASSWORD",
    "DB_DATABASE",
    "DB_HOST",
    "DB_PORT",
  ];

  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar],
  );

  if (missingEnvVars.length > 0) {
    throw new RuntimeException(
      `Missing environment variables: ${missingEnvVars.join(", ")}`,
    );
  }
}

/**
 * Configures and binds Swagger with the project's application
 * @param app The NestJS Application instance
 */
export function configureSwagger(app: INestApplication): void {
  const configService = app.get(ConfigService<IAppConfig>);
  const API_TITLE = "API Documentation";
  const API_DESCRIPTION = "API Doc. for API mock platform";
  const API_VERSION = "1.0";
  const SWAGGER_URL = "";
  const options = new DocumentBuilder()
    .setTitle(API_TITLE)
    .setDescription(API_DESCRIPTION)
    .setVersion(API_VERSION)
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(SWAGGER_URL, app, document, {
    customSiteTitle: "API Mock Platform",
    swaggerOptions: {
      docExpansion: "none",
      persistAuthorization: true,
      apisSorter: "alpha",
      operationsSorter: "method",
      tagsSorter: "alpha",
    },
  });
    }


/**
 * Generates obj for the app's CORS configurations
 * @returns CORS configurations
 */
export function corsConfig(): CorsOptions {
  return {
    allowedHeaders:
      "Origin, X-Requested-With, Content-Type, Accept, Authorization, Set-Cookie, Cookies",
    credentials: true,
    origin: "*",
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS",
  };
}

/**
 * Configure app instance
 * @param {INestApplication} app - Application instance
 */
export function configure(app: INestApplication): void {
  app.use(helmet());
  app.use(cookieParser());
  app.setGlobalPrefix("api/v1");
  app.enableCors(corsConfig());
  configureSwagger(app);
}
